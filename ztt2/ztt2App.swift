//
//  ztt2App.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI
import UserNotifications
import CloudKit

@main
struct ztt2App: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var dataManager = DataManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    init() {
        // 初始化API密钥
        setupAPIKey()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(dataManager)
                .environmentObject(authManager)
                .onAppear {
                    // 应用启动时请求推送通知权限
                    requestNotificationPermission()
                }
        }
    }

    // MARK: - Private Methods

    /**
     * 设置API密钥
     */
    private func setupAPIKey() {
        let keychainManager = KeychainManager.shared

        // 如果Keychain中没有API密钥，则设置默认密钥
        if !keychainManager.hasAPIKey() {
            keychainManager.saveAPIKey("sk-eb2dc94c4f594097b7747421169b9110")
            print("🔐 已设置默认API密钥")
        } else {
            print("🔐 API密钥已存在于Keychain中")
        }
    }

    /**
     * 请求推送通知权限
     */
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if let error = error {
                print("❌ 推送通知权限请求失败: \(error.localizedDescription)")
            } else if granted {
                print("✅ 推送通知权限已授予")
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            } else {
                print("⚠️ 用户拒绝了推送通知权限")
            }
        }
    }
}

// MARK: - AppDelegate

class AppDelegate: NSObject, UIApplicationDelegate, UNUserNotificationCenterDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        // 设置通知中心代理
        UNUserNotificationCenter.current().delegate = self
        return true
    }

    // MARK: - 推送通知处理

    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        print("✅ 远程推送注册成功")
        // 设备令牌会自动传递给CloudKit
    }

    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("❌ 远程推送注册失败: \(error.localizedDescription)")
    }

    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("📱 收到远程推送通知")

        // 检查是否是CloudKit通知
        if let notification = CKNotification(fromRemoteNotificationDictionary: userInfo) {
            handleCloudKitNotification(notification)
            completionHandler(.newData)
        } else {
            completionHandler(.noData)
        }
    }

    // MARK: - UNUserNotificationCenterDelegate

    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // 应用在前台时也显示通知
        completionHandler([.alert, .sound, .badge])
    }

    // MARK: - CloudKit通知处理

    private func handleCloudKitNotification(_ notification: CKNotification) {
        print("☁️ 处理CloudKit通知: \(notification.notificationType)")

        switch notification.notificationType {
        case .query:
            if let queryNotification = notification as? CKQueryNotification {
                print("🔍 查询通知 - 记录ID: \(queryNotification.recordID?.recordName ?? "未知")")

                // 触发数据同步
                Task {
                    await triggerDataSync()
                }
            }
        case .recordZone:
            print("🗂️ 记录区域通知")
            Task {
                await triggerDataSync()
            }
        case .database:
            print("🗄️ 数据库通知")
            Task {
                await triggerDataSync()
            }
        @unknown default:
            print("❓ 未知CloudKit通知类型")
        }
    }

    @MainActor
    private func triggerDataSync() async {
        print("🔄 触发数据同步...")

        // 通知iCloudSyncManager进行同步
        await iCloudSyncManager.shared.triggerManualSync()

        // 发送本地通知，通知UI更新
        NotificationCenter.default.post(
            name: NSNotification.Name("CloudKitDataUpdated"),
            object: nil
        )
    }
}
