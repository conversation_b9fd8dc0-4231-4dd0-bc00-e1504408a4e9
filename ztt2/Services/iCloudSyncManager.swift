//
//  iCloudSyncManager.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/3.
//

import Foundation
import CoreData
import CloudKit
import SwiftUI
import Combine

/**
 * iCloud同步管理器
 * 负责管理iCloud同步功能的开启/关闭、权限检查、数据迁移等
 * 重构版本：使用NSUbiquitousKeyValueStore，支持所有用户使用
 */
@MainActor
class iCloudSyncManager: ObservableObject {

    // MARK: - Singleton
    static let shared = iCloudSyncManager()

    // MARK: - Published Properties
    @Published var isSyncEnabled: Bool = false
    @Published var syncStatus: SyncStatus = .idle
    @Published var lastSyncDate: Date?
    @Published var isAvailable: Bool = false
    @Published var errorMessage: String?

    // MARK: - Private Properties
    private let persistenceController = PersistenceController.shared
    private var cancellables = Set<AnyCancellable>()
    private let ubiquitousStore = NSUbiquitousKeyValueStore.default
    private let subscriptionManager = CloudKitSubscriptionManager.shared

    // MARK: - Constants
    private let syncEnabledKey = "icloud_sync_enabled"
    private let lastSyncDateKey = "last_sync_date"
    
    private init() {
        setupInitialState()
        setupSyncMonitoring()

        // 监听iCloud Key-Value Store变化
        NotificationCenter.default.publisher(for: NSUbiquitousKeyValueStore.didChangeExternallyNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleExternalSyncSettingChange()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查iCloud同步是否可用
     */
    func checkAvailability() async {
        // 使用PersistenceController的方法检查可用性
        let available = await persistenceController.checkCloudKitAvailability()

        await MainActor.run {
            self.isAvailable = available
            if !available {
                self.errorMessage = "icloud_sync.error.not_available".localized
            } else {
                self.errorMessage = nil
            }
        }
    }
    
    /**
     * 检查用户是否有权限使用iCloud同步
     * 新版本：所有用户都可以使用iCloud同步
     */
    func hasPermission() -> Bool {
        // 移除付费限制，所有用户都可以使用iCloud同步
        return true
    }
    
    /**
     * 尝试开启iCloud同步
     */
    func enableSync() async -> Bool {
        // 1. 检查权限（现在所有用户都有权限）
        guard hasPermission() else {
            await MainActor.run {
                self.errorMessage = "icloud_sync.error.permission_denied".localized
            }
            return false
        }

        // 2. 检查iCloud可用性
        await checkAvailability()
        guard isAvailable else {
            return false
        }

        // 3. 使用PersistenceController启用CloudKit同步
        let success = await persistenceController.enableCloudKitSync()

        if success {
            // 设置CloudKit订阅以实现实时同步
            await subscriptionManager.setupSubscriptions()

            await MainActor.run {
                self.isSyncEnabled = true
                self.syncStatus = .success
                self.ubiquitousStore.set(true, forKey: self.syncEnabledKey)
                self.ubiquitousStore.synchronize()
                self.lastSyncDate = Date()
                self.ubiquitousStore.set(Date(), forKey: self.lastSyncDateKey)
                print("✅ iCloud同步已启用")
            }
        } else {
            await MainActor.run {
                self.syncStatus = .failed
                self.errorMessage = "icloud_sync.error.enable_failed".localized
            }
        }

        return success
    }

    /**
     * 关闭iCloud同步
     */
    func disableSync(deleteCloudData: Bool = false) async -> Bool {
        // 移除CloudKit订阅
        await subscriptionManager.removeAllSubscriptions()

        let success = await persistenceController.disableCloudKitSync(deleteCloudData: deleteCloudData)

        if success {
            await MainActor.run {
                self.isSyncEnabled = false
                self.syncStatus = .idle
                self.ubiquitousStore.set(false, forKey: self.syncEnabledKey)
                self.ubiquitousStore.synchronize()
                print("⏸️ iCloud同步已关闭")
            }
        }

        return success
    }
    
    /**
     * 手动触发同步
     */
    func triggerManualSync() async {
        guard isSyncEnabled && isAvailable else {
            errorMessage = "icloud_sync.error.not_available".localized
            return
        }

        await MainActor.run {
            self.syncStatus = .syncing
        }

        // 保存当前上下文以触发同步
        persistenceController.save()

        // 等待一段时间让同步完成
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒

        await MainActor.run {
            self.syncStatus = .success
            self.lastSyncDate = Date()
            self.ubiquitousStore.set(Date(), forKey: self.lastSyncDateKey)
            self.ubiquitousStore.synchronize()
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置初始状态
     */
    private func setupInitialState() {
        // 从NSUbiquitousKeyValueStore读取同步状态
        isSyncEnabled = ubiquitousStore.bool(forKey: syncEnabledKey)

        if let lastSyncTimestamp = ubiquitousStore.object(forKey: lastSyncDateKey) as? Date {
            lastSyncDate = lastSyncTimestamp
        }

        // 同步PersistenceController的状态
        persistenceController.isCloudKitEnabled = isSyncEnabled

        // 异步检查可用性
        Task {
            await checkAvailability()
        }
    }
    
    /**
     * 处理外部同步设置变化
     * 当其他设备修改了iCloud Key-Value Store中的同步设置时调用
     */
    private func handleExternalSyncSettingChange() {
        let externalSyncEnabled = ubiquitousStore.bool(forKey: syncEnabledKey)

        if externalSyncEnabled != isSyncEnabled {
            print("🔄 检测到外部同步设置变化: \(externalSyncEnabled)")

            Task {
                if externalSyncEnabled {
                    _ = await enableSync()
                } else {
                    _ = await disableSync()
                }
            }
        }
    }



    /**
     * 设置同步状态监控
     */
    private func setupSyncMonitoring() {
        // 监听CloudKit通知
        NotificationCenter.default.addObserver(
            forName: .NSPersistentStoreRemoteChange,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                self?.handleRemoteChange(notification)
            }
        }

        // 定期检查同步状态
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.checkSyncHealth()
            }
            .store(in: &cancellables)
    }

    /**
     * 处理远程数据变化
     */
    private func handleRemoteChange(_ notification: Notification) {
        guard isSyncEnabled else { return }

        print("📡 检测到远程数据变化")

        // 更新同步状态
        if syncStatus != .syncing {
            syncStatus = .syncing

            // 延迟更新为成功状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                self.syncStatus = .success
                self.lastSyncDate = Date()
                self.ubiquitousStore.set(Date(), forKey: self.lastSyncDateKey)
                self.ubiquitousStore.synchronize()
            }
        }
    }

    /**
     * 检查同步健康状态
     */
    private func checkSyncHealth() {
        guard isSyncEnabled && isAvailable else { return }

        // 检查是否长时间未同步
        if let lastSync = lastSyncDate,
           Date().timeIntervalSince(lastSync) > 3600 { // 1小时
            print("⚠️ 长时间未同步，建议手动同步")
        }
    }
    







}

// MARK: - Sync Status Enum

enum SyncStatus {
    case idle
    case syncing
    case migrating
    case success
    case failed

    var displayText: String {
        switch self {
        case .idle:
            return "icloud_sync.status.idle".localized
        case .syncing:
            return "icloud_sync.status.syncing".localized
        case .migrating:
            return "icloud_sync.status.migrating".localized
        case .success:
            return "icloud_sync.status.success".localized
        case .failed:
            return "icloud_sync.status.failed".localized
        }
    }

    var iconName: String {
        switch self {
        case .idle:
            return "icloud"
        case .syncing, .migrating:
            return "icloud.and.arrow.up"
        case .success:
            return "icloud.and.arrow.up.fill"
        case .failed:
            return "icloud.slash"
        }
    }
}


