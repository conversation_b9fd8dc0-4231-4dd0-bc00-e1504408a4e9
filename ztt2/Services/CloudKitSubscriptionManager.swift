//
//  CloudKitSubscriptionManager.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/3.
//

import Foundation
import CloudKit
import Combine

/**
 * CloudKit订阅管理器
 * 负责管理CloudKit推送通知订阅，实现实时数据同步
 */
@MainActor
class CloudKitSubscriptionManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = CloudKitSubscriptionManager()
    
    // MARK: - Properties
    private let container: CKContainer
    private let database: CKDatabase
    private var subscriptions: [CKSubscription] = []
    
    // MARK: - Constants
    private let subscriptionPrefix = "ztt2_subscription_"
    
    private init() {
        self.container = CKContainer(identifier: "iCloud.com.rainkygong.ztt2")
        self.database = container.privateCloudDatabase
    }
    
    // MARK: - Public Methods
    
    /**
     * 设置所有必要的CloudKit订阅
     */
    func setupSubscriptions() async {
        print("🔔 开始设置CloudKit订阅...")
        
        do {
            // 1. 清理现有订阅
            await cleanupExistingSubscriptions()
            
            // 2. 创建新订阅
            try await createDataSubscriptions()
            
            print("✅ CloudKit订阅设置完成")
        } catch {
            print("❌ CloudKit订阅设置失败: \(error.localizedDescription)")
        }
    }
    
    /**
     * 移除所有订阅
     */
    func removeAllSubscriptions() async {
        print("🗑️ 移除所有CloudKit订阅...")
        await cleanupExistingSubscriptions()
    }
    
    // MARK: - Private Methods
    
    /**
     * 清理现有订阅
     */
    private func cleanupExistingSubscriptions() async {
        do {
            let existingSubscriptions = try await database.allSubscriptions()
            
            for subscription in existingSubscriptions {
                if subscription.subscriptionID.hasPrefix(subscriptionPrefix) {
                    try await database.deleteSubscriptionWithID(subscription.subscriptionID)
                    print("🗑️ 删除订阅: \(subscription.subscriptionID)")
                }
            }
        } catch {
            print("❌ 清理订阅失败: \(error.localizedDescription)")
        }
    }
    
    /**
     * 创建数据订阅
     */
    private func createDataSubscriptions() async throws {
        // 为每个重要的数据实体创建订阅
        let entityTypes = ["User", "Member", "PointRecord", "DiaryEntry", "AIReport"]
        
        for entityType in entityTypes {
            try await createSubscription(for: entityType)
        }
    }
    
    /**
     * 为指定实体类型创建订阅
     */
    private func createSubscription(for recordType: String) async throws {
        let subscriptionID = "\(subscriptionPrefix)\(recordType)"
        
        // 创建查询订阅 - 监听所有记录变化
        let predicate = NSPredicate(value: true)
        let subscription = CKQuerySubscription(
            recordType: recordType,
            predicate: predicate,
            subscriptionID: subscriptionID,
            options: [.firesOnRecordCreation, .firesOnRecordUpdate, .firesOnRecordDeletion]
        )
        
        // 配置通知信息
        let notificationInfo = CKSubscription.NotificationInfo()
        notificationInfo.shouldSendContentAvailable = true // 静默推送
        notificationInfo.shouldBadge = false
        notificationInfo.shouldSendMutableContent = true
        
        subscription.notificationInfo = notificationInfo
        
        do {
            let savedSubscription = try await database.save(subscription)
            print("✅ 创建订阅成功: \(savedSubscription.subscriptionID)")
        } catch {
            print("❌ 创建订阅失败 (\(recordType)): \(error.localizedDescription)")
            throw error
        }
    }
}

// MARK: - CloudKit Database Extensions

extension CKDatabase {
    /**
     * 获取所有订阅
     */
    func allSubscriptions() async throws -> [CKSubscription] {
        return try await withCheckedThrowingContinuation { continuation in
            self.fetchAllSubscriptions { subscriptions, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    continuation.resume(returning: subscriptions ?? [])
                }
            }
        }
    }
    
    /**
     * 删除订阅
     */
    func deleteSubscriptionWithID(_ subscriptionID: String) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            self.delete(withSubscriptionID: subscriptionID) { _, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    continuation.resume(returning: ())
                }
            }
        }
    }
    
    /**
     * 保存订阅
     */
    func save(_ subscription: CKSubscription) async throws -> CKSubscription {
        return try await withCheckedThrowingContinuation { continuation in
            self.save(subscription) { savedSubscription, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if let savedSubscription = savedSubscription {
                    continuation.resume(returning: savedSubscription)
                } else {
                    continuation.resume(throwing: CKError(.internalError))
                }
            }
        }
    }
}
